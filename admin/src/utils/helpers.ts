/**
 * 获取完整的图片URL
 * @param imageUrl - 相对图片URL（如：/uploads/pets/xxx.png）
 * @returns 完整的图片URL，如果输入为空则返回空字符串
 */
export const getFullImageUrl = (imageUrl: string | null | undefined): string => {
  if (!imageUrl) return ''

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }

  // 在开发环境中，Vite代理会处理 /uploads 路径
  // 在生产环境中，需要完整的URL
  if (import.meta.env.DEV) {
    return imageUrl // 开发环境使用代理
  } else {
    // 生产环境使用环境变量配置的API服务器地址
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://8.217.56.113:3000'
    return `${apiBaseUrl}${imageUrl}`
  }
}

/**
 * 格式化日期
 * @param date - 日期字符串或Date对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date): string => {
  if (!date) return ''

  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

/**
 * 格式化文件大小
 * @param bytes - 字节数
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
