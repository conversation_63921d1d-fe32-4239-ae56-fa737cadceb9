/* 移除了Leaflet地图样式 */

@import "tailwindcss";

/* 定义自定义颜色 - Tailwind CSS v4 语法 */
@theme {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-300: #fca5a5;
  --color-red-400: #f87171;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  --color-red-700: #b91c1c;
  --color-red-800: #991b1b;
  --color-red-900: #7f1d1d;

  --color-yellow-50: #fefce8;
  --color-yellow-100: #fef3c7;
  --color-yellow-200: #fde68a;
  --color-yellow-300: #fcd34d;
  --color-yellow-400: #fbbf24;
  --color-yellow-500: #f59e0b;
  --color-yellow-600: #d97706;
  --color-yellow-700: #b45309;
  --color-yellow-800: #92400e;
  --color-yellow-900: #78350f;

  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-200: #bbf7d0;
  --color-green-300: #86efac;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;
  --color-green-800: #166534;
  --color-green-900: #14532d;
}

/* 自定义样式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }

  /* 移动端优化 */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-40;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-600 hover:text-primary-600 transition-colors;
  }

  .mobile-nav-item.active {
    @apply text-primary-600;
  }

  /* 响应式容器 */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 响应式网格 */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  /* 响应式文本 */
  .text-responsive-xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .text-responsive-lg {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  /* 触摸友好的按钮 */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* 模态框响应式 */
  .modal-responsive {
    @apply w-full max-w-lg mx-4 sm:mx-auto;
  }

  /* 表单响应式 */
  .form-responsive {
    @apply space-y-4 sm:space-y-6;
  }

  .form-grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6;
  }
}

/* 移除了Leaflet图标相关样式 */

/* 移动端优化 */
@media (max-width: 768px) {
  /* 减少移动端的内边距 */
  .mobile-padding {
    @apply px-4 py-4;
  }

  /* 移动端全宽按钮 */
  .mobile-full-width {
    @apply w-full;
  }

  /* 移动端文本大小调整 */
  .mobile-text-sm {
    @apply text-sm;
  }

  /* 移动端卡片间距 */
  .mobile-card-spacing {
    @apply space-y-4;
  }

  /* 移动端模态框 */
  .mobile-modal {
    @apply fixed inset-x-4 top-8 bottom-8 max-h-none overflow-y-auto;
  }

  /* 移动端表单 */
  .mobile-form-spacing {
    @apply space-y-4;
  }

  /* 移动端网格调整 */
  .mobile-grid-1 {
    @apply grid-cols-1;
  }

  /* 移动端隐藏 */
  .mobile-hidden {
    @apply hidden;
  }

  /* 移动端显示 */
  .mobile-show {
    @apply block;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid-2 {
    @apply grid-cols-2;
  }

  .tablet-padding {
    @apply px-6 py-6;
  }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
  .desktop-grid-3 {
    @apply grid-cols-3;
  }

  .desktop-grid-4 {
    @apply grid-cols-4;
  }

  .desktop-padding {
    @apply px-8 py-8;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 增加触摸目标大小 */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* 移除悬停效果 */
  .no-hover {
    @apply hover:bg-transparent hover:text-current;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply border-2 border-black;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .reduce-motion {
    @apply transition-none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 暗色模式支持（预留） */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
}

/* 打印样式 */
@media print {
  .print-hidden {
    @apply hidden;
  }

  .print-show {
    @apply block;
  }

  body {
    @apply text-black bg-white;
  }
}
