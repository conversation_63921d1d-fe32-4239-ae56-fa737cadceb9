<template>
  <div
    @click="$emit('click', post)"
    class="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer overflow-hidden"
  >
    <!-- 宠物照片 -->
    <div class="aspect-w-16 aspect-h-9">
      <img
        v-if="post.pet?.photo_url"
        :src="getFullImageUrl(post.pet.photo_url)"
        :alt="post.pet.name"
        class="w-full h-48 object-cover"
      />
      <div
        v-else
        class="w-full h-48 bg-gray-200 flex items-center justify-center"
      >
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="p-4">
      <!-- 状态标签 -->
      <div class="flex items-center justify-between mb-3">
        <span
          :class="getStatusClass(post.post_status)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getStatusText(post.post_status) }}
        </span>
        <span class="text-xs text-gray-500">
          {{ formatDate(post.last_seen_time) }}
        </span>
      </div>

      <!-- 宠物基本信息 -->
      <div class="space-y-2">
        <h3 class="text-lg font-semibold text-gray-900 truncate">
          {{ post.pet?.name || $t('common.unknown') }}
        </h3>

        <div class="grid grid-cols-2 gap-2 text-sm text-gray-600">
          <div class="flex items-center">
            <span class="font-medium">{{ $t('pet.species') }}:</span>
            <span class="ml-1 truncate">{{ getPetSpeciesLabel(post.pet?.species) }}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium">{{ $t('pet.color') }}:</span>
            <span class="ml-1 truncate">{{ getPetColorLabel(post.pet?.color) }}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium">{{ $t('pet.gender') }}:</span>
            <span class="ml-1">{{ getPetGenderLabel(post.pet?.gender) }}</span>
          </div>
          <div class="flex items-center" v-if="post.pet?.breed">
            <span class="font-medium">{{ $t('pet.breed') }}:</span>
            <span class="ml-1 truncate">{{ post.pet.breed }}</span>
          </div>
        </div>

        <!-- 走失地点 -->
        <div class="flex items-start mt-3">
          <svg class="w-4 h-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span class="text-sm text-gray-600 line-clamp-2">
            {{ post.last_seen_location }}
          </span>
        </div>

        <!-- 线索数量 -->
        <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
          <div class="flex items-center text-sm text-gray-500">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            {{ post.sightings_count || 0 }} {{ $t('sighting.count') }}
          </div>
          <span class="text-xs text-gray-400">
            {{ $t('common.clickToView') }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Post } from '@/types'
import { formatDate, getFullImageUrl } from '@/utils/helpers'
import { usePetSpeciesOptions, usePetColorOptions, usePetGenderOptions } from '@/utils/i18n-options'

interface Props {
  post: Post
}

defineProps<Props>()
defineEmits<{
  click: [post: Post]
}>()

const { t } = useI18n()
const petSpeciesOptions = usePetSpeciesOptions()
const petColorOptions = usePetColorOptions()
const petGenderOptions = usePetGenderOptions()

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case 'searching':
      return 'bg-yellow-100 text-yellow-800'
    case 'found':
      return 'bg-green-100 text-green-800'
    case 'closed':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  return t(`post.status.${status}`)
}

// 获取宠物种类标签
const getPetSpeciesLabel = (species?: string) => {
  if (!species) return t('common.unknown')
  const option = petSpeciesOptions.find((opt: any) => opt.value === species)
  return option?.label || species
}

// 获取宠物颜色标签
const getPetColorLabel = (color?: string) => {
  if (!color) return t('common.unknown')
  const option = petColorOptions.find((opt: any) => opt.value === color)
  return option?.label || color
}

// 获取宠物性别标签
const getPetGenderLabel = (gender?: string) => {
  if (!gender) return t('common.unknown')
  const option = petGenderOptions.find((opt: any) => opt.value === gender)
  return option?.label || gender
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
