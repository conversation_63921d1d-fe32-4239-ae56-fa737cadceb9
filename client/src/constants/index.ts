// 宠物品种选项 - 使用键值对应翻译
export const PET_SPECIES_OPTIONS = [
  { value: 'bird', key: 'pet.species.bird' },
  { value: 'cat', key: 'pet.species.cat' },
  { value: 'turtle', key: 'pet.species.turtle' },
  { value: 'dog', key: 'pet.species.dog' },
  { value: 'rabbit', key: 'pet.species.rabbit' },
  { value: 'reptile', key: 'pet.species.reptile' },
  { value: 'rodent', key: 'pet.species.rodent' },
  { value: 'other', key: 'pet.species.other' },
]

// 宠物性别选项 - 使用键值对应翻译
export const PET_GENDERS_OPTIONS = [
  { value: 'male', key: 'pet.gender.male' },
  { value: 'female', key: 'pet.gender.female' },
  { value: 'male_neutered', key: 'pet.gender.male_neutered' },
  { value: 'female_neutered', key: 'pet.gender.female_neutered' },
  { value: 'unknown', key: 'pet.gender.unknown' },
]

// 宠物毛色选项 - 使用键值对应翻译
export const PET_COLORS_OPTIONS = [
  { value: 'black', key: 'pet.colors.black' },
  { value: 'white', key: 'pet.colors.white' },
  { value: 'brown', key: 'pet.colors.brown' },
  { value: 'golden', key: 'pet.colors.golden' },
  { value: 'gray', key: 'pet.colors.gray' },
  { value: 'mixed', key: 'pet.colors.mixed' },
  { value: 'other', key: 'pet.colors.other' },
]

// 向后兼容的旧常量（逐步迁移）
export const PET_SPECIES = [
  { value: 'dog', label: '狗' },
  { value: 'cat', label: '猫' },
  { value: 'rabbit', label: '兔子' },
  { value: 'bird', label: '鸟类' },
  { value: 'hamster', label: '仓鼠' },
  { value: 'other', label: '其他' },
]

export const PET_GENDERS = [
  { value: 'male', label: '雄性' },
  { value: 'female', label: '雌性' },
  { value: 'unknown', label: '不确定' },
]

export const PET_COLORS = [
  { value: 'black', label: '黑色' },
  { value: 'white', label: '白色' },
  { value: 'brown', label: '棕色' },
  { value: 'golden', label: '金色' },
  { value: 'gray', label: '灰色' },
  { value: 'mixed', label: '花色' },
  { value: 'other', label: '其他' },
]

// 香港地区选项 - 使用键值对应翻译
export const HK_DISTRICTS_OPTIONS = [
  { value: 'central', key: 'location.districts.central' },
  { value: 'wanchai', key: 'location.districts.wanchai' },
  { value: 'eastern', key: 'location.districts.eastern' },
  { value: 'southern', key: 'location.districts.southern' },
  { value: 'yauTsimMong', key: 'location.districts.yauTsimMong' },
  { value: 'shamShuiPo', key: 'location.districts.shamShuiPo' },
  { value: 'kowloonCity', key: 'location.districts.kowloonCity' },
  { value: 'wongTaiSin', key: 'location.districts.wongTaiSin' },
  { value: 'kwunTong', key: 'location.districts.kwunTong' },
  { value: 'tsuen', key: 'location.districts.tsuen' },
  { value: 'tuenMun', key: 'location.districts.tuenMun' },
  { value: 'yuenLong', key: 'location.districts.yuenLong' },
  { value: 'north', key: 'location.districts.north' },
  { value: 'taiPo', key: 'location.districts.taiPo' },
  { value: 'shaTin', key: 'location.districts.shaTin' },
  { value: 'saiKung', key: 'location.districts.saiKung' },
  { value: 'kwaiTsing', key: 'location.districts.kwaiTsing' },
  { value: 'islands', key: 'location.districts.islands' },
]

// 向后兼容的旧常量（逐步迁移）
export const HK_DISTRICTS = [
  { value: 'central', label: '中西区' },
  { value: 'wanchai', label: '湾仔区' },
  { value: 'eastern', label: '东区' },
  { value: 'southern', label: '南区' },
  { value: 'yauTsimMong', label: '油尖旺区' },
  { value: 'shamShuiPo', label: '深水埗区' },
  { value: 'kowloonCity', label: '九龙城区' },
  { value: 'wongTaiSin', label: '黄大仙区' },
  { value: 'kwunTong', label: '观塘区' },
  { value: 'tsuen', label: '荃湾区' },
  { value: 'tuenMun', label: '屯门区' },
  { value: 'yuenLong', label: '元朗区' },
  { value: 'north', label: '北区' },
  { value: 'taiPo', label: '大埔区' },
  { value: 'shaTin', label: '沙田区' },
  { value: 'saiKung', label: '西贡区' },
  { value: 'kwaiTsing', label: '葵青区' },
  { value: 'islands', label: '离岛区' },
]

// 帖子状态
export const POST_STATUS = {
  SEARCHING: 'searching',
  FOUND: 'found',
  CLOSED: 'closed',
} as const

export const POST_STATUS_LABELS = {
  [POST_STATUS.SEARCHING]: '寻找中',
  [POST_STATUS.FOUND]: '已找到',
  [POST_STATUS.CLOSED]: '已关闭',
}

// 审核状态
export const ADMIN_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
} as const

export const ADMIN_STATUS_LABELS = {
  [ADMIN_STATUS.PENDING]: '待审核',
  [ADMIN_STATUS.APPROVED]: '已通过',
  [ADMIN_STATUS.REJECTED]: '已拒绝',
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 12,
  MAX_LIMIT: 50,
}

// 文件上传配置
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 5,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
}

// 移除了地图配置

// 社交分享平台
export const SHARE_PLATFORMS = [
  {
    name: 'WhatsApp',
    icon: 'whatsapp',
    color: '#25D366',
    getUrl: (text: string, url: string) => `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`,
  },
  {
    name: 'Facebook',
    icon: 'facebook',
    color: '#1877F2',
    getUrl: (text: string, url: string) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
  },
  {
    name: 'Twitter',
    icon: 'twitter',
    color: '#1DA1F2',
    getUrl: (text: string, url: string) => `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
  },
  {
    name: 'Telegram',
    icon: 'telegram',
    color: '#0088CC',
    getUrl: (text: string, url: string) => `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
  },
]

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限执行此操作',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入信息有误，请检查后重试',
  FILE_TOO_LARGE: '文件大小超过限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
}

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  LOGOUT_SUCCESS: '登出成功',
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  UPLOAD_SUCCESS: '上传成功',
  COPY_SUCCESS: '复制成功',
}
