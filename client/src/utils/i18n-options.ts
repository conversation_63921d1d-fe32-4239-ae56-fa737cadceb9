import { useI18n } from 'vue-i18n'
import { 
  PET_SPECIES_OPTIONS, 
  PET_GENDERS_OPTIONS, 
  PET_COLORS_OPTIONS, 
  HK_DISTRICTS_OPTIONS 
} from '@/constants'

/**
 * 获取国际化的宠物品种选项
 */
export function usePetSpeciesOptions() {
  const { t } = useI18n()
  
  return PET_SPECIES_OPTIONS.map(option => ({
    value: option.value,
    label: t(option.key)
  }))
}

/**
 * 获取国际化的宠物性别选项
 */
export function usePetGenderOptions() {
  const { t } = useI18n()
  
  return PET_GENDERS_OPTIONS.map(option => ({
    value: option.value,
    label: t(option.key)
  }))
}

/**
 * 获取国际化的宠物颜色选项
 */
export function usePetColorOptions() {
  const { t } = useI18n()
  
  return PET_COLORS_OPTIONS.map(option => ({
    value: option.value,
    label: t(option.key)
  }))
}

/**
 * 获取国际化的香港地区选项
 */
export function useHKDistrictOptions() {
  const { t } = useI18n()
  
  return HK_DISTRICTS_OPTIONS.map(option => ({
    value: option.value,
    label: t(option.key)
  }))
}

/**
 * 获取国际化的帖子状态选项
 */
export function usePostStatusOptions() {
  const { t } = useI18n()
  
  return [
    { value: 'searching', label: t('post.status.searching') },
    { value: 'found', label: t('post.status.found') },
    { value: 'closed', label: t('post.status.closed') }
  ]
}
