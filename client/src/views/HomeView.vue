<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-gray-900">{{ $t('app.title') }}</h1>
          </div>

          <div class="flex items-center space-x-4">
            <!-- 语言切换 -->
            <LanguageSwitch />

            <template v-if="authStore.isAuthenticated">
              <span class="text-gray-700">{{ $t('common.welcome') }}，{{ authStore.user?.username }}</span>
              <button
                @click="handleLogout"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {{ $t('auth.logout') }}
              </button>
            </template>
            <template v-else>
              <router-link
                to="/login"
                class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                {{ $t('auth.login') }}
              </router-link>
              <router-link
                to="/register"
                class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {{ $t('auth.register') }}
              </router-link>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-responsive py-6">
      <div class="mobile-padding sm:px-0">
        <div class="text-center">
          <h2 class="text-responsive-xl font-extrabold text-gray-900">
            {{ $t('app.subtitle') }}
          </h2>
          <p class="mt-4 text-base sm:text-lg text-gray-600">
            {{ $t('app.description') }}
          </p>

          <div class="mt-8 flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <router-link
              to="/post/create"
              class="btn-primary btn-touch mobile-full-width sm:w-auto px-6 py-3 text-lg"
            >
              {{ $t('post.create') }}
            </router-link>
            <router-link
              to="/posts"
              class="btn-secondary btn-touch mobile-full-width sm:w-auto px-6 py-3 text-lg"
            >
              {{ $t('nav.posts') }}
            </router-link>
          </div>
        </div>

        <!-- 最新尋寵信息 -->
        <div class="mt-12 sm:mt-16">
          <div class="text-center mb-8">
            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900">
              {{ $t('app.recentPosts.title') }}
            </h2>
            <p class="mt-2 text-base sm:text-lg text-gray-600">
              {{ $t('app.recentPosts.description') }}
            </p>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-2 text-gray-600">{{ $t('common.loading') }}</span>
          </div>

          <!-- 帖子卡片网格 -->
          <div v-else-if="recentPosts.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <PetCard
              v-for="post in recentPosts"
              :key="post.id"
              :post="post"
              @click="handleCardClick"
            />
          </div>

          <!-- 无数据状态 -->
          <div v-else class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('app.recentPosts.noPosts') }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ $t('app.recentPosts.noPostsDescription') }}</p>
          </div>

          <!-- 查看全部按钮 -->
          <div v-if="recentPosts.length > 0" class="text-center mt-8">
            <button
              @click="goToPostsList"
              class="btn-secondary btn-touch px-6 py-3 text-lg"
            >
              {{ $t('app.recentPosts.viewAll') }}
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import LanguageSwitch from '@/components/LanguageSwitch.vue'
import PetCard from '@/components/PetCard.vue'
import { postService } from '@/services/posts'
import type { Post, SearchFilters, PaginationParams } from '@/types'

const router = useRouter()
const authStore = useAuthStore()

// 最新帖子数据
const recentPosts = ref<Post[]>([])
const loading = ref(false)

// 处理登出
const handleLogout = async () => {
  await authStore.logout()
  router.push('/')
}

// 加载最新帖子
const loadRecentPosts = async () => {
  try {
    loading.value = true

    const filters: SearchFilters = {}
    const params: PaginationParams = {
      page: 1,
      limit: 6, // 只显示最新的6个帖子
    }

    const response = await postService.getList(filters, params)

    if (response.success && response.data) {
      recentPosts.value = response.data as Post[]
    }
  } catch (error) {
    console.error('加载最新帖子失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理卡片点击
const handleCardClick = (post: Post) => {
  router.push(`/posts/${post.id}`)
}

// 跳转到帖子列表页面
const goToPostsList = () => {
  router.push('/posts')
}

// 组件挂载时加载数据
onMounted(() => {
  loadRecentPosts()
})
</script>
