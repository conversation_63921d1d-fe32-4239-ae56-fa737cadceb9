-- 论坛功能相关表设计

-- -----------------------------------------------------
-- Table `post_comments` (帖子评论表)
-- 存储帖子的评论和讨论
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `post_comments` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `post_id` INT UNSIGNED NOT NULL COMMENT '关联的帖子ID',
  `user_id` INT UNSIGNED NULL COMMENT '评论用户ID (NULL表示匿名)',
  `parent_id` INT UNSIGNED NULL COMMENT '父评论ID (用于回复)',
  `content` TEXT NOT NULL COMMENT '评论内容',
  `anonymous_name` VARCHAR(50) NULL COMMENT '匿名用户昵称',
  `contact_info` VARCHAR(255) NULL COMMENT '联系方式 (可选)',
  `is_helpful` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否被标记为有用',
  `helpful_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '有用标记数量',
  `is_pinned` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否置顶 (帖子作者可设置)',
  `is_hidden` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否隐藏 (管理员可设置)',
  `reporter_ip` VARCHAR(45) NULL COMMENT '提交者IP地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_post_id` (`post_id` ASC),
  INDEX `idx_user_id` (`user_id` ASC),
  INDEX `idx_parent_id` (`parent_id` ASC),
  INDEX `idx_created_at` (`created_at` DESC),
  CONSTRAINT `fk_comments_posts`
    FOREIGN KEY (`post_id`)
    REFERENCES `posts` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
  CONSTRAINT `fk_comments_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `users` (`id`)
    ON DELETE SET NULL
    ON UPDATE CASCADE,
  CONSTRAINT `fk_comments_parent`
    FOREIGN KEY (`parent_id`)
    REFERENCES `post_comments` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '帖子评论表';

-- -----------------------------------------------------
-- Table `comment_helpful_votes` (评论有用投票表)
-- 记录用户对评论的有用投票
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `comment_helpful_votes` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '投票ID',
  `comment_id` INT UNSIGNED NOT NULL COMMENT '评论ID',
  `user_id` INT UNSIGNED NULL COMMENT '投票用户ID (NULL表示匿名)',
  `voter_ip` VARCHAR(45) NULL COMMENT '投票者IP地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投票时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `unique_user_comment` (`comment_id`, `user_id`),
  UNIQUE INDEX `unique_ip_comment` (`comment_id`, `voter_ip`),
  CONSTRAINT `fk_votes_comments`
    FOREIGN KEY (`comment_id`)
    REFERENCES `post_comments` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
  CONSTRAINT `fk_votes_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `users` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '评论有用投票表';

-- -----------------------------------------------------
-- Table `sighting_comments` (线索评论表)
-- 针对具体线索的评论和讨论
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `sighting_comments` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `sighting_id` INT UNSIGNED NOT NULL COMMENT '关联的线索ID',
  `user_id` INT UNSIGNED NULL COMMENT '评论用户ID (NULL表示匿名)',
  `content` TEXT NOT NULL COMMENT '评论内容',
  `anonymous_name` VARCHAR(50) NULL COMMENT '匿名用户昵称',
  `comment_type` ENUM('discussion', 'verification', 'additional_info') NOT NULL DEFAULT 'discussion' COMMENT '评论类型',
  `is_hidden` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否隐藏',
  `reporter_ip` VARCHAR(45) NULL COMMENT '提交者IP地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  PRIMARY KEY (`id`),
  INDEX `idx_sighting_id` (`sighting_id` ASC),
  INDEX `idx_user_id` (`user_id` ASC),
  INDEX `idx_created_at` (`created_at` DESC),
  CONSTRAINT `fk_sighting_comments_sightings`
    FOREIGN KEY (`sighting_id`)
    REFERENCES `sightings` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
  CONSTRAINT `fk_sighting_comments_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `users` (`id`)
    ON DELETE SET NULL
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '线索评论表';

-- -----------------------------------------------------
-- Table `post_updates` (帖子更新表)
-- 帖子作者发布的更新信息
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `post_updates` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '更新ID',
  `post_id` INT UNSIGNED NOT NULL COMMENT '关联的帖子ID',
  `title` VARCHAR(255) NOT NULL COMMENT '更新标题',
  `content` TEXT NOT NULL COMMENT '更新内容',
  `update_type` ENUM('progress', 'found', 'reward', 'thanks', 'other') NOT NULL DEFAULT 'progress' COMMENT '更新类型',
  `is_pinned` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否置顶显示',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_post_id` (`post_id` ASC),
  INDEX `idx_created_at` (`created_at` DESC),
  CONSTRAINT `fk_updates_posts`
    FOREIGN KEY (`post_id`)
    REFERENCES `posts` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '帖子更新表';
