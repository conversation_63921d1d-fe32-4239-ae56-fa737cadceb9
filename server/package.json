{"name": "lostpethk-server", "version": "1.0.0", "description": "Lost Pet Hong Kong - Backend API Server", "main": "src/app.js", "type": "module", "scripts": {"dev": "nodemon src/app.js", "start": "node src/app.js", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/", "create-admin": "node scripts/createAdmin.js", "check": "node --check src/app.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "joi": "^17.11.0", "dotenv": "^16.3.1", "nodemailer": "^6.9.7", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "prettier": "^3.1.0", "@types/node": "^20.10.4"}, "keywords": ["pet", "lost", "finder", "hongkong", "express", "mysql"], "author": "Lost Pet HK Team", "license": "MIT"}