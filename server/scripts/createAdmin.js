import { Admin } from '../src/models/Admin.js';
import { hashPassword } from '../src/utils/password.js';
import { testConnection } from '../config/db.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * 创建默认管理员账户
 */
async function createDefaultAdmin() {
  try {
    console.log('🔄 正在连接数据库...');
    
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败');
      process.exit(1);
    }

    const defaultUsername = 'admin';
    const defaultPassword = 'admin123';

    // 检查管理员是否已存在
    const existingAdmin = await Admin.findByUsername(defaultUsername);
    if (existingAdmin) {
      console.log('ℹ️  默认管理员账户已存在');
      console.log(`用户名: ${defaultUsername}`);
      console.log('如需重置密码，请手动删除后重新创建');
      return;
    }

    // 加密密码
    console.log('🔐 正在加密密码...');
    const passwordHash = await hashPassword(defaultPassword);

    // 创建管理员
    console.log('👤 正在创建管理员账户...');
    const newAdmin = await Admin.create({
      username: defaultUsername,
      password_hash: passwordHash,
      role: 'superadmin'
    });

    console.log('✅ 默认管理员账户创建成功！');
    console.log('='.repeat(40));
    console.log(`用户名: ${defaultUsername}`);
    console.log(`密码: ${defaultPassword}`);
    console.log(`角色: ${newAdmin.role}`);
    console.log(`创建时间: ${newAdmin.created_at}`);
    console.log('='.repeat(40));
    console.log('⚠️  请及时修改默认密码！');

  } catch (error) {
    console.error('❌ 创建管理员失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  createDefaultAdmin()
    .then(() => {
      console.log('🎉 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

export { createDefaultAdmin };
