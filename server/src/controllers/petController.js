import { Pet } from '../models/Pet.js';
import { 
  successResponse, 
  errorResponse, 
  notFoundResponse,
  forbiddenResponse,
  paginatedResponse,
  validationErrorResponse 
} from '../utils/response.js';
import { getFileUrl, deleteFile } from '../utils/upload.js';

/**
 * 创建宠物
 */
export async function createPet(req, res) {
  try {
    const userId = req.user.id;
    const petData = { ...req.body, user_id: userId };

    // 如果有上传的照片，设置照片URL
    if (req.file) {
      petData.photo_url = getFileUrl(req.file.filename, 'pets');
    }

    const newPet = await Pet.create(petData);

    return successResponse(res, newPet.toJSON(), '宠物信息创建成功', 201);

  } catch (error) {
    console.error('创建宠物失败:', error);
    
    // 如果创建失败且有上传的文件，删除文件
    if (req.file) {
      await deleteFile(`uploads/pets/${req.file.filename}`);
    }
    
    return errorResponse(res, '创建宠物信息失败，请稍后重试', 500);
  }
}

/**
 * 获取用户的宠物列表
 */
export async function getUserPets(req, res) {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const pets = await Pet.findByUserId(userId, { page, limit });
    const total = await Pet.countByUserId(userId);

    return paginatedResponse(res, pets.map(pet => pet.toJSON()), {
      page,
      limit,
      total
    }, '获取宠物列表成功');

  } catch (error) {
    console.error('获取宠物列表失败:', error);
    return errorResponse(res, '获取宠物列表失败，请稍后重试', 500);
  }
}

/**
 * 根据ID获取宠物详情
 */
export async function getPetById(req, res) {
  try {
    const petId = parseInt(req.params.id);
    
    if (isNaN(petId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的宠物ID' }]);
    }

    const pet = await Pet.findById(petId);
    
    if (!pet) {
      return notFoundResponse(res, '宠物不存在');
    }

    return successResponse(res, pet.toJSON(), '获取宠物详情成功');

  } catch (error) {
    console.error('获取宠物详情失败:', error);
    return errorResponse(res, '获取宠物详情失败，请稍后重试', 500);
  }
}

/**
 * 更新宠物信息
 */
export async function updatePet(req, res) {
  try {
    const petId = parseInt(req.params.id);
    const userId = req.user.id;
    
    if (isNaN(petId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的宠物ID' }]);
    }

    // 检查宠物是否存在
    const existingPet = await Pet.findById(petId);
    if (!existingPet) {
      return notFoundResponse(res, '宠物不存在');
    }

    // 检查宠物是否属于当前用户
    const belongsToUser = await Pet.belongsToUser(petId, userId);
    if (!belongsToUser) {
      return forbiddenResponse(res, '您没有权限修改此宠物信息');
    }

    const updateData = { ...req.body };

    // 如果有上传新照片，更新照片URL并删除旧照片
    if (req.file) {
      updateData.photo_url = getFileUrl(req.file.filename, 'pets');
      
      // 删除旧照片
      if (existingPet.photo_url) {
        const oldPhotoPath = existingPet.photo_url.replace('/', '');
        await deleteFile(oldPhotoPath);
      }
    }

    const updatedPet = await Pet.update(petId, updateData);

    return successResponse(res, updatedPet.toJSON(), '宠物信息更新成功');

  } catch (error) {
    console.error('更新宠物失败:', error);
    
    // 如果更新失败且有上传的文件，删除文件
    if (req.file) {
      await deleteFile(`uploads/pets/${req.file.filename}`);
    }
    
    return errorResponse(res, '更新宠物信息失败，请稍后重试', 500);
  }
}

/**
 * 删除宠物
 */
export async function deletePet(req, res) {
  try {
    const petId = parseInt(req.params.id);
    const userId = req.user.id;
    
    if (isNaN(petId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的宠物ID' }]);
    }

    // 检查宠物是否存在
    const existingPet = await Pet.findById(petId);
    if (!existingPet) {
      return notFoundResponse(res, '宠物不存在');
    }

    // 检查宠物是否属于当前用户
    const belongsToUser = await Pet.belongsToUser(petId, userId);
    if (!belongsToUser) {
      return forbiddenResponse(res, '您没有权限删除此宠物信息');
    }

    // 删除宠物照片
    if (existingPet.photo_url) {
      const photoPath = existingPet.photo_url.replace('/', '');
      await deleteFile(photoPath);
    }

    const success = await Pet.delete(petId);
    
    if (!success) {
      return errorResponse(res, '删除宠物失败', 500);
    }

    return successResponse(res, null, '宠物信息删除成功');

  } catch (error) {
    console.error('删除宠物失败:', error);
    return errorResponse(res, '删除宠物失败，请稍后重试', 500);
  }
}

/**
 * 搜索宠物
 */
export async function searchPets(req, res) {
  try {
    const { species, breed, color, gender } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const searchParams = {};
    if (species) searchParams.species = species;
    if (breed) searchParams.breed = breed;
    if (color) searchParams.color = color;
    if (gender) searchParams.gender = gender;

    const pets = await Pet.search(searchParams, { page, limit });
    const total = await Pet.countSearch(searchParams);

    return paginatedResponse(res, pets.map(pet => pet.toJSON()), {
      page,
      limit,
      total
    }, '搜索宠物成功');

  } catch (error) {
    console.error('搜索宠物失败:', error);
    return errorResponse(res, '搜索宠物失败，请稍后重试', 500);
  }
}

/**
 * 上传宠物照片
 */
export async function uploadPetPhoto(req, res) {
  try {
    if (!req.file) {
      return validationErrorResponse(res, [{ field: 'photo', message: '请选择要上传的照片' }]);
    }

    const photoUrl = getFileUrl(req.file.filename, 'pets');

    return successResponse(res, {
      photo_url: photoUrl,
      filename: req.file.filename,
      size: req.file.size,
      mimetype: req.file.mimetype
    }, '照片上传成功');

  } catch (error) {
    console.error('上传照片失败:', error);
    
    // 如果处理失败，删除已上传的文件
    if (req.file) {
      await deleteFile(`uploads/pets/${req.file.filename}`);
    }
    
    return errorResponse(res, '照片上传失败，请稍后重试', 500);
  }
}

/**
 * 获取宠物统计信息（管理员用）
 */
export async function getPetStats(req, res) {
  try {
    const stats = await Pet.getStats();

    return successResponse(res, stats, '获取宠物统计成功');

  } catch (error) {
    console.error('获取宠物统计失败:', error);
    return errorResponse(res, '获取宠物统计失败，请稍后重试', 500);
  }
}
