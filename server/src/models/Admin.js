import { query } from '../../config/db.js';

/**
 * 管理员数据模型
 */
export class Admin {
  constructor(adminData) {
    this.id = adminData.id;
    this.username = adminData.username;
    this.password_hash = adminData.password_hash;
    this.role = adminData.role;
    this.created_at = adminData.created_at;
  }

  /**
   * 创建新管理员
   * @param {Object} adminData - 管理员数据
   * @returns {Promise<Object>} 创建的管理员信息
   */
  static async create(adminData) {
    const { username, password_hash, role = 'auditor' } = adminData;
    
    const sql = `
      INSERT INTO admins (username, password_hash, role)
      VALUES (?, ?, ?)
    `;
    
    try {
      const result = await query(sql, [username, password_hash, role]);
      
      // 返回新创建的管理员信息（不包含密码）
      const newAdmin = await Admin.findById(result.insertId);
      return newAdmin;
    } catch (error) {
      console.error('创建管理员失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID查找管理员
   * @param {number} id - 管理员ID
   * @returns {Promise<Object|null>} 管理员信息或null
   */
  static async findById(id) {
    const sql = `
      SELECT id, username, role, created_at
      FROM admins 
      WHERE id = ?
    `;
    
    try {
      const results = await query(sql, [id]);
      return results.length > 0 ? new Admin(results[0]) : null;
    } catch (error) {
      console.error('查找管理员失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户名查找管理员（包含密码哈希，用于登录验证）
   * @param {string} username - 用户名
   * @returns {Promise<Object|null>} 管理员信息或null
   */
  static async findByUsernameWithPassword(username) {
    const sql = `
      SELECT id, username, password_hash, role, created_at
      FROM admins 
      WHERE username = ?
    `;
    
    try {
      const results = await query(sql, [username]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('查找管理员失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户名查找管理员
   * @param {string} username - 用户名
   * @returns {Promise<Object|null>} 管理员信息或null
   */
  static async findByUsername(username) {
    const sql = `
      SELECT id, username, role, created_at
      FROM admins 
      WHERE username = ?
    `;
    
    try {
      const results = await query(sql, [username]);
      return results.length > 0 ? new Admin(results[0]) : null;
    } catch (error) {
      console.error('查找管理员失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户名是否已存在
   * @param {string} username - 用户名
   * @returns {Promise<boolean>} 是否存在
   */
  static async usernameExists(username) {
    const sql = 'SELECT COUNT(*) as count FROM admins WHERE username = ?';
    
    try {
      const results = await query(sql, [username]);
      return results[0].count > 0;
    } catch (error) {
      console.error('检查管理员用户名失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有管理员列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 管理员列表
   */
  static async findAll(options = {}) {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT id, username, role, created_at
      FROM admins 
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    try {
      const results = await query(sql, [limit, offset]);
      return results.map(admin => new Admin(admin));
    } catch (error) {
      console.error('获取管理员列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取管理员总数
   * @returns {Promise<number>} 管理员总数
   */
  static async count() {
    const sql = 'SELECT COUNT(*) as count FROM admins';
    
    try {
      const results = await query(sql);
      return results[0].count;
    } catch (error) {
      console.error('获取管理员总数失败:', error);
      throw error;
    }
  }

  /**
   * 更新管理员信息
   * @param {number} id - 管理员ID
   * @param {Object} updateData - 要更新的数据
   * @returns {Promise<Object|null>} 更新后的管理员信息
   */
  static async update(id, updateData) {
    const allowedFields = ['username', 'role'];
    const updateFields = [];
    const updateValues = [];

    // 只允许更新指定字段
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(updateData[field]);
      }
    }

    if (updateFields.length === 0) {
      throw new Error('没有可更新的字段');
    }

    updateValues.push(id);
    const sql = `
      UPDATE admins 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    try {
      await query(sql, updateValues);
      return await Admin.findById(id);
    } catch (error) {
      console.error('更新管理员失败:', error);
      throw error;
    }
  }

  /**
   * 更新管理员密码
   * @param {number} id - 管理员ID
   * @param {string} newPasswordHash - 新密码哈希
   * @returns {Promise<boolean>} 是否更新成功
   */
  static async updatePassword(id, newPasswordHash) {
    const sql = `
      UPDATE admins 
      SET password_hash = ?
      WHERE id = ?
    `;

    try {
      const result = await query(sql, [newPasswordHash, id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新管理员密码失败:', error);
      throw error;
    }
  }

  /**
   * 删除管理员
   * @param {number} id - 管理员ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  static async delete(id) {
    const sql = 'DELETE FROM admins WHERE id = ?';
    
    try {
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除管理员失败:', error);
      throw error;
    }
  }

  /**
   * 转换为安全的JSON格式（不包含敏感信息）
   * @returns {Object} 安全的管理员信息
   */
  toSafeJSON() {
    return {
      id: this.id,
      username: this.username,
      role: this.role,
      created_at: this.created_at
    };
  }
}
