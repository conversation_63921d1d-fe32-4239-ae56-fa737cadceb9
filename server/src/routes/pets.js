import express from 'express';
import {
  createPet,
  getUserPets,
  getPetById,
  updatePet,
  deletePet,
  searchPets,
  uploadPetPhoto,
  getPetStats
} from '../controllers/petController.js';
import { authenticateToken, authenticateAdmin } from '../utils/jwt.js';
import { validateRequest, petInfoSchema, paginationSchema } from '../utils/validation.js';
import { uploadPetPhoto as uploadMiddleware } from '../utils/upload.js';

const router = express.Router();

// 宠物信息验证中间件
const petValidation = validateRequest(petInfoSchema, 'body');

// 分页验证中间件
const paginationValidation = validateRequest(paginationSchema, 'query');

/**
 * @route POST /api/pets
 * @desc 创建宠物信息
 * @access Private (User)
 */
router.post('/', authenticateToken, uploadMiddleware, petValidation, createPet);

/**
 * @route GET /api/pets/my
 * @desc 获取当前用户的宠物列表
 * @access Private (User)
 */
router.get('/my', authenticateToken, paginationValidation, getUserPets);

/**
 * @route GET /api/pets/search
 * @desc 搜索宠物
 * @access Public
 */
router.get('/search', paginationValidation, searchPets);

/**
 * @route POST /api/pets/upload
 * @desc 上传宠物照片
 * @access Private (User)
 */
router.post('/upload', authenticateToken, uploadMiddleware, uploadPetPhoto);

/**
 * @route GET /api/pets/stats
 * @desc 获取宠物统计信息
 * @access Private (Admin)
 */
router.get('/stats', authenticateAdmin, getPetStats);

/**
 * @route GET /api/pets/:id
 * @desc 根据ID获取宠物详情
 * @access Public
 */
router.get('/:id', getPetById);

/**
 * @route PUT /api/pets/:id
 * @desc 更新宠物信息
 * @access Private (User - 仅宠物主人)
 */
router.put('/:id', authenticateToken, uploadMiddleware, petValidation, updatePet);

/**
 * @route DELETE /api/pets/:id
 * @desc 删除宠物信息
 * @access Private (User - 仅宠物主人)
 */
router.delete('/:id', authenticateToken, deletePet);

export default router;
