import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

const SALT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS) || 12;

/**
 * 加密密码
 * @param {string} password - 明文密码
 * @returns {Promise<string>} 加密后的密码哈希
 */
export async function hashPassword(password) {
  try {
    if (!password || typeof password !== 'string') {
      throw new Error('密码必须是非空字符串');
    }
    
    if (password.length < 6) {
      throw new Error('密码长度至少6位');
    }
    
    const salt = await bcrypt.genSalt(SALT_ROUNDS);
    const hashedPassword = await bcrypt.hash(password, salt);
    return hashedPassword;
  } catch (error) {
    console.error('密码加密失败:', error);
    throw error;
  }
}

/**
 * 验证密码
 * @param {string} password - 明文密码
 * @param {string} hashedPassword - 加密后的密码哈希
 * @returns {Promise<boolean>} 密码是否匹配
 */
export async function verifyPassword(password, hashedPassword) {
  try {
    if (!password || !hashedPassword) {
      return false;
    }
    
    const isMatch = await bcrypt.compare(password, hashedPassword);
    return isMatch;
  } catch (error) {
    console.error('密码验证失败:', error);
    return false;
  }
}

/**
 * 生成随机密码
 * @param {number} length - 密码长度，默认12位
 * @returns {string} 随机密码
 */
export function generateRandomPassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

/**
 * 验证密码强度
 * @param {string} password - 要验证的密码
 * @returns {Object} 验证结果
 */
export function validatePasswordStrength(password) {
  const result = {
    isValid: true,
    errors: [],
    score: 0
  };

  if (!password || typeof password !== 'string') {
    result.isValid = false;
    result.errors.push('密码不能为空');
    return result;
  }

  // 长度检查
  if (password.length < 6) {
    result.isValid = false;
    result.errors.push('密码长度至少6位');
  } else if (password.length >= 8) {
    result.score += 1;
  }

  // 包含小写字母
  if (/[a-z]/.test(password)) {
    result.score += 1;
  } else {
    result.errors.push('密码应包含小写字母');
  }

  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    result.score += 1;
  }

  // 包含数字
  if (/\d/.test(password)) {
    result.score += 1;
  } else {
    result.errors.push('密码应包含数字');
  }

  // 包含特殊字符
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    result.score += 1;
  }

  // 设置强度等级
  if (result.score >= 4) {
    result.strength = 'strong';
  } else if (result.score >= 3) {
    result.strength = 'medium';
  } else {
    result.strength = 'weak';
  }

  return result;
}
