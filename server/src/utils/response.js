/**
 * 统一的API响应格式工具函数
 */

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} statusCode - HTTP状态码
 */
export function successResponse(res, data = null, message = '操作成功', statusCode = 200) {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  });
}

/**
 * 错误响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP状态码
 * @param {*} errors - 详细错误信息
 */
export function errorResponse(res, message = '操作失败', statusCode = 400, errors = null) {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  if (errors) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
}

/**
 * 分页响应
 * @param {Object} res - Express响应对象
 * @param {Array} data - 数据数组
 * @param {Object} pagination - 分页信息
 * @param {string} message - 响应消息
 */
export function paginatedResponse(res, data, pagination, message = '获取成功') {
  return res.status(200).json({
    success: true,
    message,
    data,
    pagination: {
      currentPage: pagination.page,
      totalPages: Math.ceil(pagination.total / pagination.limit),
      totalItems: pagination.total,
      itemsPerPage: pagination.limit,
      hasNextPage: pagination.page < Math.ceil(pagination.total / pagination.limit),
      hasPrevPage: pagination.page > 1
    },
    timestamp: new Date().toISOString()
  });
}

/**
 * 验证错误响应
 * @param {Object} res - Express响应对象
 * @param {Array} validationErrors - 验证错误数组
 */
export function validationErrorResponse(res, validationErrors) {
  return errorResponse(res, '数据验证失败', 422, validationErrors);
}

/**
 * 未授权响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
export function unauthorizedResponse(res, message = '未授权访问') {
  return errorResponse(res, message, 401);
}

/**
 * 禁止访问响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
export function forbiddenResponse(res, message = '权限不足') {
  return errorResponse(res, message, 403);
}

/**
 * 资源未找到响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
export function notFoundResponse(res, message = '资源未找到') {
  return errorResponse(res, message, 404);
}

/**
 * 服务器内部错误响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
export function internalServerErrorResponse(res, message = '服务器内部错误') {
  return errorResponse(res, message, 500);
}

/**
 * 冲突响应（如重复数据）
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
export function conflictResponse(res, message = '数据冲突') {
  return errorResponse(res, message, 409);
}

/**
 * 请求过于频繁响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
export function tooManyRequestsResponse(res, message = '请求过于频繁，请稍后再试') {
  return errorResponse(res, message, 429);
}
