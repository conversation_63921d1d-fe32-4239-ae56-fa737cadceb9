-- -----------------------------------------------------
-- Schema pet_finder_db
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `pet_finder_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `pet_finder_db`;

-- -----------------------------------------------------
-- Table `users` (用户表)
-- 存储注册用户信息，包括宠物主人和普通用户
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `email` VARCHAR(100) NOT NULL COMMENT '电子邮箱，用于登录和接收通知',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
  `phone_number` VARCHAR(20) NULL COMMENT '联系电话',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账户创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账户更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `email_UNIQUE` (`email` ASC) VISIBLE,
  UNIQUE INDEX `username_UNIQUE` (`username` ASC) VISIBLE
) ENGINE = InnoDB COMMENT = '用户信息表';


-- -----------------------------------------------------
-- Table `pets` (宠物表)
-- 存储宠物的基本信息
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `pets` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '宠物ID',
  `user_id` INT UNSIGNED NOT NULL COMMENT '所属主人ID',
  `name` VARCHAR(50) NULL COMMENT '宠物名字',
  `species` VARCHAR(50) NOT NULL COMMENT '宠物品种 (如：猫、狗)',
  `breed` VARCHAR(50) NULL COMMENT '具体品种 (如：金毛、英短)',
  `color` VARCHAR(50) NOT NULL COMMENT '毛色',
  `gender` ENUM('male', 'female', 'male_neutered', 'female_neutered', 'unknown') NOT NULL COMMENT '性别',
  `age` INT NULL COMMENT '年龄',
  `description` TEXT NULL COMMENT '外貌特征、性格等描述',
  `photo_url` VARCHAR(255) NULL COMMENT '宠物照片URL',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '信息创建时间',
  PRIMARY KEY (`id`),
  INDEX `fk_pets_users_idx` (`user_id` ASC) VISIBLE,
  CONSTRAINT `fk_pets_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `users` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '宠物信息表';


-- -----------------------------------------------------
-- Table `posts` (寻宠帖子表)
-- 核心表，管理所有寻宠信息
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `posts` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '帖子ID',
  `user_id` INT UNSIGNED NOT NULL COMMENT '发帖人ID (主人)',
  `pet_id` INT UNSIGNED NOT NULL COMMENT '走失宠物ID',
  `last_seen_location` VARCHAR(255) NOT NULL COMMENT '最后目击地点',
  `last_seen_time` DATETIME NOT NULL COMMENT '最后目击时间',
  `post_status` ENUM('searching', 'found', 'closed') NOT NULL DEFAULT 'searching' COMMENT '帖子状态：仍在寻找, 已找到, 已关闭',
  `admin_status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' COMMENT '审核状态：待审核, 已通过, 已拒绝',
  `video_url` VARCHAR(255) NULL COMMENT '相关视频URL',
  `contact_info` VARCHAR(255) NULL COMMENT '主人留下的公开联系方式（可选）',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '帖子创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '帖子更新时间',
  PRIMARY KEY (`id`),
  INDEX `fk_posts_users_idx` (`user_id` ASC) VISIBLE,
  INDEX `fk_posts_pets_idx` (`pet_id` ASC) VISIBLE,
  CONSTRAINT `fk_posts_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `users` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
  CONSTRAINT `fk_posts_pets`
    FOREIGN KEY (`pet_id`)
    REFERENCES `pets` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '寻宠帖子信息表';


-- -----------------------------------------------------
-- Table `sightings` (目击线索表)
-- 存储匿名提交的线索
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `sightings` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `post_id` INT UNSIGNED NOT NULL COMMENT '关联的帖子ID',
  `sighting_location` VARCHAR(255) NOT NULL COMMENT '目击地点',
  `sighting_time` DATETIME NOT NULL COMMENT '目击时间',
  `sighting_photo_url` VARCHAR(255) NULL COMMENT '目击时拍摄的照片URL (仅内部可见)',
  `description` TEXT NULL COMMENT '补充描述',
  `reporter_ip` VARCHAR(45) NULL COMMENT '提交者IP地址 (用于追踪滥用行为)',
  `is_verified` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '线索是否被主人确认为有效',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '线索提交时间',
  PRIMARY KEY (`id`),
  INDEX `fk_sightings_posts_idx` (`post_id` ASC) VISIBLE,
  CONSTRAINT `fk_sightings_posts`
    FOREIGN KEY (`post_id`)
    REFERENCES `posts` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '目击线索回报表';


-- -----------------------------------------------------
-- Table `notifications` (通知表)
-- 用于实现邮件/推送通知功能
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` INT UNSIGNED NOT NULL COMMENT '接收通知的用户ID',
  `type` ENUM('post_approved', 'new_sighting') NOT NULL COMMENT '通知类型：帖子通过审核, 收到新线索',
  `related_id` INT UNSIGNED NOT NULL COMMENT '关联ID (如帖子ID或线索ID)',
  `message` VARCHAR(255) NOT NULL COMMENT '通知内容',
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已读',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '通知创建时间',
  PRIMARY KEY (`id`),
  INDEX `fk_notifications_users_idx` (`user_id` ASC) VISIBLE,
  CONSTRAINT `fk_notifications_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `users` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '用户通知表';


-- -----------------------------------------------------
-- Table `admins` (管理员表)
-- 存储后台管理员账户信息
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `admins` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` VARCHAR(50) NOT NULL COMMENT '管理员用户名',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
  `role` VARCHAR(50) NOT NULL DEFAULT 'auditor' COMMENT '角色 (如：auditor, superadmin)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账户创建时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `username_UNIQUE` (`username` ASC) VISIBLE
) ENGINE = InnoDB COMMENT = '后台管理员账户表';

-- Active: 1752025475794@@127.0.0.1@3306@pet_finder_db
-- 创建邮箱配置表
CREATE TABLE IF NOT EXISTS `email_config` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `smtp_host` VARCHAR(255) NOT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` INT NOT NULL DEFAULT 587 COMMENT 'SMTP端口',
  `smtp_secure` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否使用安全连接',
  `smtp_user` VARCHAR(255) NOT NULL COMMENT 'SMTP用户名（邮箱地址）',
  `smtp_pass` VARCHAR(255) NOT NULL COMMENT 'SMTP密码或授权码',
  `from_name` VARCHAR(100) NOT NULL COMMENT '发件人名称',
  `from_email` VARCHAR(255) NOT NULL COMMENT '发件人邮箱',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT = '邮箱配置表';

-- 创建邮箱验证码表
CREATE TABLE IF NOT EXISTS `email_verification_codes` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '验证码ID',
  `email` VARCHAR(255) NOT NULL COMMENT '邮箱地址',
  `code` VARCHAR(10) NOT NULL COMMENT '验证码',
  `type` ENUM('register', 'reset_password') NOT NULL DEFAULT 'register' COMMENT '验证码类型',
  `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
  `is_used` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已使用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_email_type` (`email`, `type`),
  INDEX `idx_expires_at` (`expires_at`)
) ENGINE = InnoDB COMMENT = '邮箱验证码表';

-- 插入默认的163邮箱配置（需要管理员后续配置具体的邮箱和密码）
INSERT INTO `email_config` (
  `smtp_host`, 
  `smtp_port`, 
  `smtp_secure`, 
  `smtp_user`, 
  `smtp_pass`, 
  `from_name`, 
  `from_email`, 
  `is_active`
) VALUES (
  'smtp.163.com',
  587,
  TRUE,
  '<EMAIL>',
  'your_auth_code',
  '走失宠物协寻平台',
  '<EMAIL>',
  FALSE
) ON DUPLICATE KEY UPDATE id=id;
